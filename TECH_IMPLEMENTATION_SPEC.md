# Stake Betting Marketplace - Technical Implementation Specification

## Project Overview
Build a digital marketplace for selling stake betting prediction tools and strategies. Products include Mines predictors, Crash predictors, and betting strategies, all priced at INR 499.

## Technology Stack

### Frontend
- **Framework:** React 18+ with TypeScript
- **Styling:** Tailwind CSS
- **State Management:** Zustand
- **Routing:** React Router v6
- **HTTP Client:** Axios
- **Forms:** React Hook Form + Yup validation
- **UI Components:** Headless UI or Radix UI

### Backend
- **Runtime:** Node.js 18+ with Express.js
- **Language:** TypeScript
- **Database:** PostgreSQL with Prisma ORM
- **Authentication:** JWT with refresh tokens
- **File Storage:** Local storage with secure access
- **Email:** Nodemailer
- **Payment:** Stripe + Razorpay

### Infrastructure
- **Environment:** Docker for development
- **Process Management:** PM2
- **Security:** Helmet.js, CORS, rate limiting
- **Validation:** Joi for API validation

## Database Schema

### Core Tables (PostgreSQL)

```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('customer', 'admin')),
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN ('mines_predictor', 'crash_predictor', 'betting_strategy')),
    price DECIMAL(10,2) DEFAULT 499.00,
    currency VARCHAR(3) DEFAULT 'INR',
    is_active BOOLEAN DEFAULT TRUE,
    download_url VARCHAR(500),
    access_instructions TEXT,
    image_url VARCHAR(500),
    features JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Orders table
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'failed', 'refunded')),
    payment_intent_id VARCHAR(255),
    payment_method VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Order items table
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL
);

-- Shopping cart table
CREATE TABLE cart_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- User purchases (for digital product access)
CREATE TABLE user_purchases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    access_granted BOOLEAN DEFAULT TRUE,
    download_count INTEGER DEFAULT 0,
    max_downloads INTEGER DEFAULT 5,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints Specification

### Authentication Routes
```
POST   /api/auth/register          # User registration
POST   /api/auth/login             # User login
POST   /api/auth/logout            # User logout
POST   /api/auth/refresh           # Refresh JWT token
POST   /api/auth/forgot-password   # Password reset request
POST   /api/auth/reset-password    # Password reset confirmation
GET    /api/auth/verify-email/:token # Email verification
```

### User Routes
```
GET    /api/users/profile          # Get user profile
PUT    /api/users/profile          # Update user profile
GET    /api/users/orders           # Get user order history
GET    /api/users/purchases        # Get user purchased products
GET    /api/users/downloads/:productId # Download purchased product
```

### Product Routes
```
GET    /api/products               # List products (with pagination, filtering)
GET    /api/products/:id           # Get single product details
GET    /api/products/categories    # Get product categories
GET    /api/products/search        # Search products
```

### Cart Routes
```
GET    /api/cart                   # Get user's cart
POST   /api/cart/add               # Add item to cart
PUT    /api/cart/update/:itemId    # Update cart item quantity
DELETE /api/cart/remove/:itemId    # Remove item from cart
DELETE /api/cart/clear             # Clear entire cart
```

### Order & Payment Routes
```
POST   /api/orders/create          # Create new order
GET    /api/orders/:id             # Get order details
POST   /api/payments/create-intent # Create Stripe payment intent
POST   /api/payments/webhook       # Handle payment webhooks
GET    /api/payments/status/:orderId # Check payment status
```

### Admin Routes (Protected)
```
GET    /api/admin/products         # List all products
POST   /api/admin/products         # Create new product
PUT    /api/admin/products/:id     # Update product
DELETE /api/admin/products/:id     # Delete product
GET    /api/admin/orders           # List all orders
GET    /api/admin/users            # List all users
GET    /api/admin/analytics        # Sales analytics
POST   /api/admin/products/:id/upload # Upload product files
```

## Key Features Implementation

### 1. Authentication System
- JWT access tokens (15 min expiry) + refresh tokens (7 days)
- Password hashing with bcrypt (12+ salt rounds)
- Email verification required
- Rate limiting: 5 login attempts per 15 minutes

### 2. Product Management
- File upload for product downloads (PDF, ZIP, EXE files)
- Secure download links with expiration
- Product categorization and filtering
- Image upload for product thumbnails

### 3. Shopping Cart
- Session-based cart for guests
- Persistent cart for logged-in users
- Real-time cart updates
- Cart abandonment handling

### 4. Payment Processing
- Stripe for international payments
- Razorpay for Indian market
- Webhook handling for payment confirmation
- Automatic order fulfillment

### 5. Digital Product Delivery
- Secure download links (24-hour expiry)
- Download attempt limits (5 per purchase)
- Email delivery with access instructions
- Account-based product access

### 6. Admin Panel
- Product CRUD operations
- Order management and tracking
- User management
- Sales analytics dashboard
- File upload management

## Security Requirements

### API Security
- HTTPS only in production
- CORS configuration for specific domains
- Rate limiting (100 requests/15 min per IP)
- Input validation and sanitization
- SQL injection prevention via ORM
- XSS protection headers

### Authentication Security
- Secure JWT implementation
- Password strength requirements
- Account lockout after failed attempts
- Secure password reset flow
- Email verification mandatory

### Payment Security
- PCI compliance via Stripe/Razorpay
- No card data storage
- Webhook signature verification
- Idempotent payment processing

## File Structure

```
stake-marketplace/
├── backend/
│   ├── src/
│   │   ├── controllers/
│   │   ├── middleware/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   ├── utils/
│   │   └── app.ts
│   ├── prisma/
│   │   ├── schema.prisma
│   │   └── migrations/
│   ├── uploads/
│   └── package.json
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── store/
│   │   ├── types/
│   │   └── App.tsx
│   └── package.json
├── docker-compose.yml
└── README.md
```

## Environment Variables

### Backend (.env)
```
DATABASE_URL=postgresql://user:password@localhost:5432/stake_marketplace
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-refresh-secret-key
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
RAZORPAY_KEY_ID=rzp_test_...
RAZORPAY_KEY_SECRET=your-razorpay-secret
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
FRONTEND_URL=http://localhost:3000
UPLOAD_DIR=./uploads
```

### Frontend (.env)
```
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_...
REACT_APP_RAZORPAY_KEY_ID=rzp_test_...
```

## Development Setup Instructions

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Docker (optional)

### Quick Start
1. Clone repository
2. Install dependencies: `npm install` in both frontend/ and backend/
3. Setup PostgreSQL database
4. Run migrations: `npx prisma migrate dev`
5. Seed database: `npx prisma db seed`
6. Start backend: `npm run dev` (port 5000)
7. Start frontend: `npm start` (port 3000)

## Testing Requirements
- Unit tests for all services and utilities
- Integration tests for API endpoints
- E2E tests for critical user flows
- Payment flow testing with test cards
- File upload/download testing

## Deployment Checklist
- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] SSL certificates installed
- [ ] Payment webhooks configured
- [ ] Email service configured
- [ ] File upload permissions set
- [ ] Rate limiting configured
- [ ] Error monitoring setup
- [ ] Backup strategy implemented

## Performance Targets
- Page load time: < 3 seconds
- API response time: < 500ms
- Database query time: < 100ms
- File download speed: > 1MB/s
- Concurrent users: 1000+

## Compliance & Legal
- GDPR compliance for EU users
- Data retention policies
- Terms of service for digital products
- Refund policy implementation
- Age verification (18+)
- Responsible gambling disclaimers

## Sample Code Implementations

### JWT Authentication Middleware (TypeScript)
```typescript
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';

interface AuthRequest extends Request {
  user?: { id: string; email: string; role: string };
}

export const authenticateToken = (req: AuthRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET!, (err, user) => {
    if (err) return res.status(403).json({ error: 'Invalid token' });
    req.user = user as any;
    next();
  });
};
```

### Product Service Example
```typescript
export class ProductService {
  async getProducts(filters: ProductFilters) {
    const { category, search, page = 1, limit = 12 } = filters;

    const where: any = { is_active: true };
    if (category) where.category = category;
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    const products = await prisma.product.findMany({
      where,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { created_at: 'desc' }
    });

    const total = await prisma.product.count({ where });

    return {
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }
}
```

### React Component Example (Product Card)
```tsx
interface ProductCardProps {
  product: Product;
  onAddToCart: (productId: string) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart }) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <img
        src={product.image_url || '/placeholder.jpg'}
        alt={product.name}
        className="w-full h-48 object-cover"
      />
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-2">{product.name}</h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {product.description}
        </p>
        <div className="flex justify-between items-center">
          <span className="text-2xl font-bold text-green-600">
            ₹{product.price}
          </span>
          <button
            onClick={() => onAddToCart(product.id)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  );
};
```

## Package.json Dependencies

### Backend Dependencies
```json
{
  "dependencies": {
    "express": "^4.18.2",
    "typescript": "^5.0.0",
    "@types/express": "^4.17.17",
    "prisma": "^5.0.0",
    "@prisma/client": "^5.0.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "joi": "^17.9.0",
    "helmet": "^7.0.0",
    "cors": "^2.8.5",
    "express-rate-limit": "^6.7.0",
    "multer": "^1.4.5-lts.1",
    "nodemailer": "^6.9.0",
    "stripe": "^12.0.0",
    "razorpay": "^2.8.6",
    "dotenv": "^16.0.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.22",
    "ts-node": "^10.9.0",
    "@types/bcryptjs": "^2.4.2",
    "@types/jsonwebtoken": "^9.0.2",
    "@types/multer": "^1.4.7",
    "@types/nodemailer": "^6.4.8"
  }
}
```

### Frontend Dependencies
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^5.0.0",
    "react-router-dom": "^6.11.0",
    "axios": "^1.4.0",
    "zustand": "^4.3.0",
    "react-hook-form": "^7.44.0",
    "yup": "^1.2.0",
    "@hookform/resolvers": "^3.1.0",
    "tailwindcss": "^3.3.0",
    "@headlessui/react": "^1.7.0",
    "@heroicons/react": "^2.0.0",
    "react-hot-toast": "^2.4.0",
    "@stripe/stripe-js": "^1.54.0",
    "@stripe/react-stripe-js": "^2.1.0"
  }
}
```

## Docker Configuration

### docker-compose.yml
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: stake_marketplace
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=********************************************/stake_marketplace
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend/uploads:/app/uploads

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5000/api

volumes:
  postgres_data:
```

---

**Implementation Priority Order:**
1. Database setup and basic API structure
2. User authentication system
3. Product catalog and management
4. Shopping cart functionality
5. Payment integration
6. Digital product delivery
7. Admin panel
8. Testing and optimization

**Estimated Development Time:** 8-12 weeks for MVP

This specification provides everything needed to build the complete stake betting marketplace with any AI coding assistant.
